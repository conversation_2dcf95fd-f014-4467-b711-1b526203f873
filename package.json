{"name": "pill-splitter-challenge-submission", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@nanostores/react": "^1.0.0", "@tailwindcss/vite": "^4.1.11", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "nanostores": "^1.0.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-redux": "^9.2.0", "redux": "^5.0.1", "tailwindcss": "^4.1.11", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0"}}